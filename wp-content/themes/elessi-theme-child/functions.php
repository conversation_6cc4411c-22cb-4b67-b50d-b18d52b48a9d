<?php
//
// Recommended way to include parent theme styles.
// (Please see http://codex.wordpress.org/Child_Themes#How_to_Create_a_Child_Theme)
//  
add_action('wp_enqueue_scripts', 'theme_enqueue_styles', 998);
function theme_enqueue_styles() {
    $prefix = function_exists('elessi_prefix_theme') ? elessi_prefix_theme() : 'elessi';
    wp_enqueue_style($prefix . '-style', get_template_directory_uri() . '/style.css',array(),'2.80','all');
    wp_enqueue_style($prefix . '-fontawesome', get_template_directory_uri() . '/assets/font-awesome-5.15.4/font-awesome.min.css',array(),'2.80','all');
    wp_enqueue_style($prefix . '-child-style', get_stylesheet_uri(),array(),'2.80','all');
}

add_filter( 'woocommerce_default_address_fields' , 'bbloomer_rename', 9999 );
function bbloomer_rename( $fields ) {
    $fields['city']['label'] = esc_html__('State', 'elessi-theme');
    $fields['state']['required'] = true;
    $fields['state']['label'] = esc_html__('City', 'elessi-theme');
    $fields['address_1']['label'] = esc_html__('Address Or National Address', 'elessi-theme');
    return $fields;
}

add_filter('woocommerce_checkout_fields', 'custom_override_checkout_fields',9999);
function custom_override_checkout_fields($fields) {
    unset($fields['billing']['billing_postcode']); 
    unset($fields['billing']['billing_company']);
    unset($fields['billing']['billing_address_2']);
    unset($fields['billing']['billing_city']);
    unset($fields['shipping']['shipping_postcode']);
    unset($fields['shipping']['shipping_company']); 
    unset($fields['shipping']['shipping_address_2']);
    unset($fields['shipping']['shipping_city']);
    $fields['billing']['billing_address_1']['placeholder'] = esc_html__('Address Or National Address', 'elessi-theme');
    $fields['shipping']['shipping_address_1']['placeholder'] = esc_html__('Address Or National Address', 'elessi-theme');
    return $fields;
}

// Adds image to WooCommerce order emails
function w3p_add_image_to_wc_emails( $args ) {
    $args['show_image'] = true;
    $args['image_size'] = array( 100, 50 );
    return $args;
}
add_filter( 'woocommerce_email_order_items_args', 'w3p_add_image_to_wc_emails' );


add_action('user_register', 'check_user_role_after_registration', 10, 1);
function check_user_role_after_registration($user_id) {
    $user = get_userdata($user_id);
    if ($user) {
        $roles = $user->roles;
        if (in_array('administrator', $roles)) {
            // Remove the user or set a different role
            wp_delete_user($user_id); // Optional: Delete user
            // Or assign a different role
            // $user->set_role('subscriber');
        }
    }
}

add_filter('pre_insert_user_data', 'restrict_user_roles', 10, 2);
function restrict_user_roles($user_data, $update) {
    if (isset($user_data['role']) && in_array($user_data['role'], ['administrator'])) {
        $user_data['role'] = 'subscriber'; // Set to a default role or another role of your choice
    }
    return $user_data;
}


// Add a custom REST API endpoint
add_action('rest_api_init', function () {
    register_rest_route('geidea/v1', '/webhook/', array(
        'methods' => 'POST',
        'callback' => 'handle_geidea_webhook',
        'permission_callback' => '__return_true', // Make sure to secure this in production
    ));
});

// Callback function to handle the webhook
function handle_geidea_webhook(WP_REST_Request $request) {
    $data = $request->get_json_params();

    // Log the incoming data for debugging
    error_log(print_r($data, true));

    // Extract posOrderId and newOrderStatus from the received payload
    $posOrderId = isset($data['posOrderId']) ? sanitize_text_field($data['posOrderId']) : null;
    $newOrderStatus = isset($data['newOrderStatus']) ? sanitize_text_field($data['newOrderStatus']) : null;

    if ($posOrderId && $newOrderStatus) {
        // Process the order (e.g., fetch details and create log)
        error_log("POS Order ID: $posOrderId, New Order Status: $newOrderStatus");

        // Fetch order details from Geidea API
        $orderDetails = fetch_order_details_from_geidea($posOrderId);

        if ($orderDetails) {
            // Log order details instead of sending to a third party
            log_order_details($orderDetails);
        }

        // Respond to Geidea to confirm receipt of the webhook
        return new WP_REST_Response('Webhook received '.print_r($orderDetails).' - '.$posOrderId.'', 200);
    } else {
        // Log an error if the payload is invalid
        error_log("Invalid payload received");
        return new WP_REST_Response('Invalid payload', 400);
    }
}

// Hook to register the webhook when the theme is activated
// add_action('after_setup_theme', 'register_geidea_webhook');
// Optional: Reset webhook registration if needed
// function reset_webhook_registration() {
//     delete_option('geidea_webhook_registered');
// }

/////////////////////////// excute code

function fetch_order_details_from_geidea($orderId) {
    $storeId = 'e0f647d3-780e-440d-a560-26e8e7d4ab62';
    $url = "https://eats-api.geideapos.net/open-api/v1/store/$storeId/order/$orderId";
    $args = array(
        'headers' => array(
            'Authorization' => 'Basic eXFhVnBtVUtCWEhXMzFMNDRDWkF0cjFJODc2bHhSYjhjbUlrcGt2Nm1SOmZ0RTZGSE9VMmRPRVJ3NHFHb3BreE1mcVFleVlNbk1wREl4VVV5S3dyT2NudkhlN2RmMzkyR29RU2haSDZtbmYxa3F4bU5zYWpPbTRPZ2dBZ04zQmR1Z0dnaXQ0OE41TDhyalJicQ==', // Your actual token
        ),
    );

    $response = wp_remote_get($url, $args);

    if (is_wp_error($response)) {
        error_log("Error fetching order details: " . $response->get_error_message());
        return false;
    }

    return json_decode(wp_remote_retrieve_body($response), true);
}

// Log order details
function log_order_details($orderDetails) {
    $log = "Order Details: " . print_r($orderDetails, true);
    error_log($log); // Log order details to error_log file

    // Optionally, write to a custom log file
    $log_file = ABSPATH . 'order_logs.txt'; // Path to log file
    $log_entry = date("Y-m-d H:i:s") . " - " . $log . PHP_EOL;

    // Append log entry to the file
    file_put_contents($log_file, $log_entry, FILE_APPEND);
}

/*************************************************************/

function custom_variable_product_sale_percentage() {
    global $product;

    if (!$product->is_type('variable')) {
        return;
    }

    // Get all variations and their prices
    $available_variations = $product->get_available_variations();
    $regular_prices = [];
    $sale_prices = [];
    $percentage = [];

    foreach ($available_variations as $variation) {
        $diff = $variation['display_regular_price'] - $variation['display_price'];
        $regular_prices[] = (float) $variation['display_regular_price'];
        $sale_prices[] = (float) $variation['display_price'];
        $percentage[] = round(($diff / $variation['display_regular_price']) * 100);
    }
    
    // Get the max regular price and min sale price to calculate the discount
    if (!empty($percentage) && max($percentage)!=0) {
        $max_perc = max($percentage);
        echo '<div class="nasa-badges-wrap variation"><span class="variant onsale badge sale-label">-' . $max_perc . '%</span></div>';
    }
}

add_action('woocommerce_before_shop_loop_item_title', 'custom_variable_product_sale_percentage');
// add_action('woocommerce_single_product_summary', 'custom_variable_product_sale_percentage', 10);
add_action( 'woocommerce_before_single_product_summary', 'custom_variable_product_sale_percentage', 10 );

add_action('elementor/element/image-carousel/section_image_carousel/before_section_end', function ($widget) {
    $widget->add_control(
        'custom_links',
        [
            'label' => __('Custom Links', 'text-domain'),
            'type' => \Elementor\Controls_Manager::TEXTAREA,
            'description' => __('Enter links separated by commas. Each link corresponds to an image.', 'text-domain'),
        ]
    );
}, 10, 1);

add_filter('elementor/widget/render_content', function ($content, $widget) {
    // Check if it's the Image Carousel widget
    if ('image-carousel' === $widget->get_name()) {
        $settings = $widget->get_settings_for_display();

        // Check if custom links are defined
        if (!empty($settings['custom_links'])) {
            $custom_links = explode(',', $settings['custom_links']); // Split the links by comma

            // Load the widget's current output HTML
            $dom = new DOMDocument();
            @$dom->loadHTML(mb_convert_encoding($content, 'HTML-ENTITIES', 'UTF-8'));

            // Find all images in the carousel
            $figures = $dom->getElementsByTagName('figure');

            foreach ($figures as $index => $figure) {
                // Wrap each figure with a link if a corresponding custom link exists
                if (isset($custom_links[$index])) {
                    $link = $dom->createElement('a');
                    $link->setAttribute('href', trim($custom_links[$index]));
                    $link->setAttribute('target', '_blank'); // Optional: Open in a new tab
                    $link->appendChild($figure->cloneNode(true));
                    $figure->parentNode->replaceChild($link, $figure);
                }
            }

            // Save the modified HTML
            $content = $dom->saveHTML();
        }
    }

    return $content;
}, 10, 2);

function custom_restrict_billing_fields() {
    ?>
    <script type="text/javascript">
        jQuery(document).ready(function($) {
            function restrictInput(selector, pattern) {
                $(selector).on('input', function() {
                    let value = $(this).val();
                    let filteredValue = value.replace(pattern, '');
                    $(this).val(filteredValue);
                });
            }

            // Allow Arabic, English letters, numbers, and spaces for billing address
            restrictInput('#billing_address_1', /[^\p{L}\p{N}\s]/gu);

            // Allow only Arabic and English letters and spaces for first and last name
            restrictInput('#billing_first_name, #billing_last_name', /[^\p{L}\s]/gu);
        });
    </script>
    <?php
}
add_action('wp_footer', 'custom_restrict_billing_fields');

// Add "Order Time" column after "Order Date"
add_filter('manage_edit-shop_order_columns', 'add_order_time_column');
function add_order_time_column($columns) {
    $new_columns = [];

    foreach ($columns as $key => $column) {
        $new_columns[$key] = $column;

        if ($key === 'order_date') {
            $new_columns['order_time'] = __('Order Time', 'woocommerce');
        }
    }

    return $new_columns;
}

// Show only the time in local timezone
add_action('manage_shop_order_posts_custom_column', 'show_order_time_column');
function show_order_time_column($column) {
    global $post;

    if ($column === 'order_time') {
        $order = wc_get_order($post->ID);
        $date_created = $order->get_date_created();

        if ($date_created) {
            $wp_timezone = wp_timezone(); // Get WP timezone setting
            $local_time = $date_created->setTimezone($wp_timezone);
            echo $local_time->format('H:i'); // e.g., 15:47 (24-hour format)
        } else {
            echo '—';
        }
    }
}

/**
 * Custom Size Guide Display Under Price
 * This function displays the size guide from category static block under the product price
 */
add_action('woocommerce_single_product_summary', 'custom_display_size_guide_under_price', 15);
function custom_display_size_guide_under_price() {
    global $product;

    if (!$product) {
        return;
    }

    // Get the size guide content using the same logic as nasa-core plugin
    $size_guide = custom_get_category_size_guide();

    if ($size_guide && $size_guide !== 'not-show') {
        echo '<div class="custom-size-guide-wrapper" style="margin: 15px 0;">';
        echo '<div class="custom-size-guide-content">';
        echo '<h4 style="margin-bottom: 10px; font-size: 16px; font-weight: 600;">' . esc_html__('Size Guide', 'elessi-theme') . '</h4>';
        echo '<div class="size-guide-content">' . $size_guide . '</div>';
        echo '</div>';
        echo '</div>';
    }
}

/**
 * Get Size Guide from Category
 * Replicates the logic from nasa-core plugin to get size guide from category
 */
function custom_get_category_size_guide() {
    global $product, $nasa_opt;

    if (!$product) {
        return false;
    }

    $size_guide = false;
    $product_id = $product->get_id();

    // Check if nasa_get_product_meta_value function exists (from nasa-core plugin)
    if (function_exists('nasa_get_product_meta_value')) {
        $p_sizeguide = nasa_get_product_meta_value($product_id, '_product_size_guide');

        // If product has specific size guide setting
        if ($p_sizeguide == '-1') {
            return 'not-show';
        }

        // If product has specific size guide block
        if ($p_sizeguide && $p_sizeguide != '') {
            if (function_exists('nasa_get_block')) {
                return nasa_get_block($p_sizeguide);
            }
        }
    }

    // Get size guide from category (this is what we want to prioritize)
    if (function_exists('nasa_root_term_id')) {
        $term_id = nasa_root_term_id();

        if ($term_id) {
            $size_guide_cat = get_term_meta($term_id, 'cat_size_guide_block', true);

            if ($size_guide_cat && $size_guide_cat != '-1') {
                if (function_exists('nasa_get_block')) {
                    $size_guide = nasa_get_block($size_guide_cat);
                }
            }

            if ($size_guide_cat == '-1') {
                return 'not-show';
            }
        }
    }

    // Fallback to theme options if no category size guide is set
    if (!$size_guide && isset($nasa_opt['size_guide_product']) && $nasa_opt['size_guide_product']) {
        if (function_exists('nasa_get_block')) {
            $size_guide = nasa_get_block($nasa_opt['size_guide_product']);
        }
    }

    return $size_guide;
}

/**
 * Add custom CSS for size guide styling
 */
add_action('wp_head', 'custom_size_guide_styles');
function custom_size_guide_styles() {
    if (is_product()) {
        ?>
        <style type="text/css">
        .custom-size-guide-wrapper {
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            padding: 15px;
            background-color: #f9f9f9;
            margin: 15px 0;
        }

        .custom-size-guide-wrapper h4 {
            margin: 0 0 10px 0;
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .custom-size-guide-wrapper .size-guide-content {
            font-size: 14px;
            line-height: 1.6;
        }

        .custom-size-guide-wrapper .size-guide-content table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .custom-size-guide-wrapper .size-guide-content table th,
        .custom-size-guide-wrapper .size-guide-content table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }

        .custom-size-guide-wrapper .size-guide-content table th {
            background-color: #f0f0f0;
            font-weight: 600;
        }

        .custom-size-guide-wrapper .size-guide-content img {
            max-width: 100%;
            height: auto;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .custom-size-guide-wrapper {
                padding: 10px;
                margin: 10px 0;
            }

            .custom-size-guide-wrapper h4 {
                font-size: 14px;
            }

            .custom-size-guide-wrapper .size-guide-content {
                font-size: 13px;
            }
        }
        </style>
        <?php
    }
}

/**
 * Optional: Remove the original popup size guide to avoid duplication
 * Uncomment the line below if you want to remove the popup version
 */
// remove_action('woocommerce_single_product_summary', 'nasa_single_product_popup_nodes', 35);

/**
 * Enhanced Size Guide Display with Collapsible Feature
 * Replace the basic version above with this enhanced version if you prefer collapsible size guide
 */
function custom_display_enhanced_size_guide_under_price() {
    global $product;

    if (!$product) {
        return;
    }

    // Get the size guide content
    $size_guide = custom_get_category_size_guide();

    if ($size_guide && $size_guide !== 'not-show') {
        ?>
        <div class="custom-size-guide-wrapper enhanced" style="margin: 15px 0;">
            <div class="size-guide-toggle" onclick="toggleSizeGuide()" style="cursor: pointer; display: flex; align-items: center; justify-content: space-between; padding: 10px; background-color: #f5f5f5; border: 1px solid #ddd; border-radius: 5px;">
                <h4 style="margin: 0; font-size: 16px; font-weight: 600;"><?php echo esc_html__('Size Guide', 'elessi-theme'); ?></h4>
                <span class="toggle-icon" style="font-size: 18px; transition: transform 0.3s;">+</span>
            </div>
            <div class="size-guide-content" style="display: none; padding: 15px; border: 1px solid #ddd; border-top: none; border-radius: 0 0 5px 5px; background-color: #fff;">
                <?php echo $size_guide; ?>
            </div>
        </div>

        <script type="text/javascript">
        function toggleSizeGuide() {
            var content = document.querySelector('.custom-size-guide-wrapper.enhanced .size-guide-content');
            var icon = document.querySelector('.custom-size-guide-wrapper.enhanced .toggle-icon');

            if (content.style.display === 'none' || content.style.display === '') {
                content.style.display = 'block';
                icon.innerHTML = '−';
                icon.style.transform = 'rotate(0deg)';
            } else {
                content.style.display = 'none';
                icon.innerHTML = '+';
                icon.style.transform = 'rotate(0deg)';
            }
        }
        </script>
        <?php
    }
}

/**
 * To use the enhanced collapsible version instead of the basic version:
 * 1. Comment out or remove the action for custom_display_size_guide_under_price
 * 2. Uncomment the line below to use the enhanced version
 */
// remove_action('woocommerce_single_product_summary', 'custom_display_size_guide_under_price', 15);
// add_action('woocommerce_single_product_summary', 'custom_display_enhanced_size_guide_under_price', 15);

/**
 * Debug function to check size guide settings for current product
 * Add ?debug_size_guide=1 to any product URL to see debug information
 * Remove this function in production
 */
add_action('wp_footer', 'debug_size_guide_info');
function debug_size_guide_info() {
    if (!is_product() || !isset($_GET['debug_size_guide']) || !current_user_can('manage_options')) {
        return;
    }

    global $product;
    if (!$product) return;

    $product_id = $product->get_id();
    $term_id = function_exists('nasa_root_term_id') ? nasa_root_term_id() : null;

    // Get all product categories
    $product_cats = wp_get_post_terms($product_id, 'product_cat');

    echo '<div style="position: fixed; bottom: 10px; right: 10px; background: #fff; border: 2px solid #333; padding: 15px; max-width: 500px; z-index: 9999; font-size: 12px; max-height: 80vh; overflow-y: auto;">';
    echo '<h4>Size Guide Debug Info</h4>';
    echo '<p><strong>Product ID:</strong> ' . $product_id . '</p>';
    echo '<p><strong>Root Category ID:</strong> ' . ($term_id ? $term_id : 'Not found') . '</p>';

    // Show all product categories
    echo '<p><strong>Product Categories:</strong></p>';
    if ($product_cats) {
        echo '<ul style="margin-left: 20px;">';
        foreach ($product_cats as $cat) {
            $size_guide_meta = get_term_meta($cat->term_id, 'cat_size_guide_block', true);
            echo '<li>' . $cat->name . ' (ID: ' . $cat->term_id . ') - Size Guide: ' . ($size_guide_meta ? $size_guide_meta : 'Not set') . '</li>';
        }
        echo '</ul>';
    }

    if (function_exists('nasa_get_product_meta_value')) {
        $p_sizeguide = nasa_get_product_meta_value($product_id, '_product_size_guide');
        echo '<p><strong>Product Size Guide Setting:</strong> ' . ($p_sizeguide ? $p_sizeguide : 'Not set') . '</p>';
    }

    if ($term_id) {
        $size_guide_cat = get_term_meta($term_id, 'cat_size_guide_block', true);
        echo '<p><strong>Root Category Size Guide Block:</strong> ' . ($size_guide_cat ? $size_guide_cat : 'Not set') . '</p>';

        // Show category edit link
        echo '<p><a href="' . admin_url('term.php?taxonomy=product_cat&tag_ID=' . $term_id . '&post_type=product') . '" target="_blank">Edit Root Category</a></p>';
    }

    $size_guide_content = custom_get_category_size_guide();
    echo '<p><strong>Final Size Guide Content:</strong> ' . ($size_guide_content ? 'Found (' . strlen($size_guide_content) . ' chars)' : 'Not found') . '</p>';

    // Show static blocks available
    if (function_exists('nasa_get_blocks_options')) {
        $blocks = nasa_get_blocks_options();
        if ($blocks) {
            echo '<p><strong>Available Static Blocks:</strong></p>';
            echo '<ul style="margin-left: 20px; max-height: 100px; overflow-y: auto;">';
            foreach ($blocks as $slug => $name) {
                echo '<li>' . $name . ' (' . $slug . ')</li>';
            }
            echo '</ul>';
        }
    }

    echo '<button onclick="this.parentElement.style.display=\'none\'">Close</button>';
    echo '</div>';
}
